# 语言枚举使用最佳实践规则

## 任务概述
基于 `system-settings/quick-message/quick-reply` 页面的最佳实践，为其他页面中的语言相关枚举提供标准化替换规则。该页面展示了如何正确使用 `Locale.Language` 枚举的完整实现模式。

## 最佳实践参考页面
**参考页面**: `src/pages/SystemSettings/QuickMessage/QuickReply.jsx`
**参考组件**: `src/pages/SystemSettings/QuickMessage/AddQuickReply.jsx`

## 核心规则

### 1. Locale.Language 枚举特殊规则
**⚠️ 重要：Locale.Language 是唯一的特殊枚举**
- **显示规则**：显示和传值都使用 `key` 字段（如 `zh_CN`, `en_US`）
- **传参规则**：API传参也使用 `key` 字段的值
- **globalConstantsManager 已内置特殊处理**：无需手动处理显示逻辑

### 2. 标准实现模式

#### 2.1 Hook导入和使用
```javascript
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const YourComponent = () => {
  // 获取全局枚举配置
  const { getSelectOptionsByKey, getEnumName } = useGlobalConstants();
  
  // 获取语言选项（推荐方式）
  const languageOptions = getSelectOptionsByKey('Locale.Language');
  
  // 带筛选条件的语言选项
  const languageOptionsWithAll = [
    { label: "全部语言", value: "全部语言" },
    ...getSelectOptionsByKey('Locale.Language')
  ];
};
```

#### 2.2 表单中的语言选择器
```javascript
<Form.Item
  name="language"
  label="语言"
  rules={[{ required: true, message: "请选择语言" }]}
>
  <Select
    placeholder="请选择语言"
    options={languageOptions}
    showSearch
    filterOption={(input, option) =>
      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
    }
  />
</Form.Item>
```

#### 2.3 表格列中的语言显示
```javascript
{
  title: "语言",
  dataIndex: "language",
  key: "language",
  width: 120,
  render: (language) => {
    // 使用全局枚举显示语言名称
    return getEnumName('Locale.Language', language) || language;
  },
}
```

#### 2.4 筛选条件中的语言选择
```javascript
<Select
  value={languageFilter}
  onChange={setLanguageFilter}
  style={{ width: 120 }}
  options={languageOptionsWithAll}
  showSearch
  filterOption={(input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  }
/>
```

#### 2.5 默认值设置
```javascript
// 表单默认值设置
form.setFieldsValue({
  language: 'zh_CN', // 默认中文
});

// 状态初始化
const [languageFilter, setLanguageFilter] = useState("全部语言");
```

## 自动替换规则

### 1. 识别需要替换的模式

#### 1.1 硬编码语言选项
**需要替换的模式**：
```javascript
// ❌ 需要替换
<Select>
  <Option value="zh_CN">中文</Option>
  <Option value="en_US">英语</Option>
  <Option value="es_ES">西班牙语</Option>
</Select>

// ❌ 需要替换
const languageOptions = [
  { label: "中文", value: "zh_CN" },
  { label: "英语", value: "en_US" },
  { label: "西班牙语", value: "es_ES" }
];
```

**替换为**：
```javascript
// ✅ 标准实现
const languageOptions = getSelectOptionsByKey('Locale.Language');

<Select
  options={languageOptions}
  showSearch
  filterOption={(input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  }
/>
```

#### 1.2 硬编码语言映射
**需要替换的模式**：
```javascript
// ❌ 需要替换
const languageMap = {
  'zh_CN': '中文',
  'en_US': '英语',
  'es_ES': '西班牙语'
};

// ❌ 需要替换
const getLanguageName = (code) => {
  switch (code) {
    case 'zh_CN': return '中文';
    case 'en_US': return '英语';
    default: return code;
  }
};
```

**替换为**：
```javascript
// ✅ 标准实现
const languageName = getEnumName('Locale.Language', code) || code;
```

### 2. 自动替换执行步骤

#### 2.1 检测语言相关字段
搜索以下模式的字段和变量：
- 字段名包含：`language`, `lang`, `locale`
- 变量名包含：`language`, `lang`, `locale`
- 注释或标签包含：`语言`, `Language`

#### 2.2 分析硬编码内容
检查是否包含常见语言代码：
- `zh_CN`, `zh_TW`, `en_US`, `en_GB`
- `es_ES`, `fr_FR`, `de_DE`, `ja_JP`
- `ko_KR`, `vi_VN`, `th_TH`, `ar_SA`

#### 2.3 匹配度评估
- **100%匹配**：使用标准语言代码且格式完全一致
- **90%以上匹配**：使用标准语言代码但可能有格式差异（如 `zh-CN` vs `zh_CN`）
- **低于90%匹配**：使用非标准语言代码或自定义格式

### 3. 替换实施规则

#### 3.1 100%匹配 - 自动替换
**条件**：硬编码使用标准语言代码格式
**操作**：立即执行代码替换，无需确认

**替换步骤**：
1. 添加 `useGlobalConstants` Hook导入
2. 获取 `getSelectOptionsByKey` 和 `getEnumName` 方法
3. 替换硬编码选项为 `getSelectOptionsByKey('Locale.Language')`
4. 替换硬编码映射为 `getEnumName('Locale.Language', value)`
5. 保持原有的搜索、筛选等功能

#### 3.2 非100%匹配 - 记录待处理
**条件**：语言代码格式不标准或有自定义逻辑
**操作**：记录到 `check_enum_record.md` 文件

**记录格式**：
```markdown
## [页面路径] - 语言枚举
- **字段名**: [字段名称]
- **当前实现**: [硬编码内容]
- **匹配的枚举**: Locale.Language
- **匹配度**: [百分比]
- **差异说明**: [具体差异，如格式不一致、包含自定义语言等]
- **建议**: [是否建议替换及注意事项]
- **时间**: [记录时间]

---
```

## 常见场景处理

### 1. 带"全部"选项的语言筛选
```javascript
// 标准实现
const languageOptions = [
  { label: "全部语言", value: "全部语言" },
  ...getSelectOptionsByKey('Locale.Language')
];
```

### 2. 多语言表单验证
```javascript
// 表单验证规则
<Form.Item
  name="language"
  label="语言"
  rules={[
    { required: true, message: "请选择语言" },
    {
      validator: (_, value) => {
        if (value && !getEnumName('Locale.Language', value)) {
          return Promise.reject(new Error('请选择有效的语言'));
        }
        return Promise.resolve();
      }
    }
  ]}
>
```

### 3. 语言相关的条件渲染
```javascript
// 根据语言显示不同内容
const isChineseLanguage = (lang) => {
  return ['zh_CN', 'zh_TW'].includes(lang);
};

// 使用枚举验证
const isValidLanguage = (lang) => {
  return getEnumName('Locale.Language', lang) !== '';
};
```

## 验证检查清单

### 1. 功能验证
- [ ] 语言选项正确显示
- [ ] 选择的语言值能正确保存
- [ ] 表格中语言显示正确
- [ ] 筛选功能正常工作
- [ ] 搜索功能正常工作

### 2. 数据验证
- [ ] 传参使用 key 字段值
- [ ] 显示使用 key 字段值（Locale.Language特殊规则）
- [ ] 默认值设置正确
- [ ] 表单回显正确

### 3. 用户体验验证
- [ ] 加载状态正确显示
- [ ] 错误处理得当
- [ ] 搜索过滤响应及时
- [ ] 无不必要的重复请求

## 注意事项

1. **Locale.Language 是特殊枚举**：显示和传值都使用 key 字段
2. **保持搜索功能**：替换后确保 showSearch 和 filterOption 正常工作
3. **保持筛选逻辑**：如"全部语言"等特殊选项需要保留
4. **默认值处理**：通常设置为 'zh_CN'
5. **向后兼容**：确保现有数据能正确显示和处理

## 实施优先级

1. **高优先级**：表单中的语言选择器
2. **中优先级**：表格中的语言显示
3. **低优先级**：筛选条件中的语言选项

通过遵循这些规则，可以确保所有页面中的语言枚举使用方式保持一致，提高代码的可维护性和用户体验的统一性。
